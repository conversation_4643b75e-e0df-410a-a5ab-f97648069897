{"businessConnection.giftReadyForBuyer": "🎁 Чудові новини! Ваш подарунок для замовлення #{orderNumber} готовий до доставки. Будь ласка, перевірте ваші замовлення.", "businessConnection.giftSentError": "❌ Не вдалося відправити подарунок релейеру. Будь ласка, спробуйте ще раз.", "businessConnection.giftSentSuccess": "✅ Подарунок успішно відправлено релейеру! Покупець буде повідомлений.", "businessConnection.giftTransferGenericError": "❌ Не вдалося передати подарунок. Будь ласка, спробуйте ще раз.", "businessConnection.giftTransferredSuccess": "✅ Подарунок успішно передано!", "businessConnection.incorrectGift": "❌ Цей подарунок не відповідає вимогам замовлення. Будь ласка, відправте правильний подарунок.", "businessConnection.noGiftToTransfer": "❌ Подарунок для передачі не знайдено.", "businessConnection.orderNotFound": "❌ Замовлення не знайдено. Будь ласка, перевірте ID замовлення та спробуйте ще раз.", "businessConnection.processingGift": "⏳ Обробляємо ваш подарунок...", "businessConnection.processingWithdrawal": "⏳ Обробляємо ваше виведення...", "businessConnection.withdrawalError": "❌ Не вдалося вивести подарунок. Будь ласка, спробуйте ще раз.", "businessConnection.withdrawalSuccess": "✅ Подарунок успішно виведено!", "buttons.backToOrders": "🔙 Назад до замовлень", "buttons.buyOrders": "🛒 Замовлення на покупку", "buttons.cancel": "❌ Скасувати", "buttons.contactSupport": "📞 Зв'язатися з підтримкою", "buttons.depositGift": "📦 Внести подарунок", "buttons.myGifts": "🎁 Мої подарунки", "buttons.openMarketplace": "🌐 Відкрити маркетплейс", "buttons.openMarketplaceButton": "🌐 Відкрити маркетплейс", "buttons.orderHelpButton": "📋 Допомога по замовленню", "buttons.readyToSendGift": "🎁 Я готовий відправити подарунок", "buttons.sellOrders": "💰 Замовлення на продаж", "buttons.viewAllOrders": "📋 Переглянути всі замовлення", "buttons.viewMyOrders": "👤 Переглянути мої замовлення", "buttons.withdrawGift": "Вивести {giftName} {orderInfo}", "callbacks.backToMenu": "🏠 Назад до головного меню", "callbacks.buyOrdersTitle": "🛒 Ваші замовлення на покупку (всього {count})", "commands.health.description": "Перевірити стан бота", "commands.health.error": "❌ Помилка при перевірці стану", "commands.health.lastCheck": "{status}\n\nОстання перевірка: {timestamp}", "commands.health.noData": "❌ Дані перевірки стану не знайдено", "commands.health.statusHealthy": "✅ Працює", "commands.health.statusUnhealthy": "⚠️ Не працює", "commands.help.description": "Показати довідкову інформацію", "commands.start.description": "Запустити бота та показати головне меню", "common.botDescription": "Ласкаво просимо до PREM🎁 – першого ліквідного передринку для неполіпшених подарунків Telegram!\nЗ PREM ви можете:\n\nЯк покупець:\n\n🔓 Купити будь-який неполіпшений подарунок TG.\n💸 Перепродати для миттєвого прибутку\n\nЯк продавець:\n\n🎁 Продати неполіпшений подарунок TG лише з 50% заставою.\n💰 Заробляти комісії з перепродажів\n\nНасолоджуйтесь швидкою, безпечною та простою торгівлею подарунками!", "common.botGenericError": "Вибачте, щось пішло не так. Будь ласка, спробуйте пізніше.", "common.botShortDescription": "🎁 PREM - Маркетплейс подарунків Telegram", "common.genericError": "❌ Не вдалося обробити ваш запит. Будь ласка, спробуйте пізніше.", "common.help": "Ласкаво просимо до PREM! \n \n@prem_channel - Спільнота \n@prem_support_official - Підтримка \n{relayerUsername} - Релейер подарунків", "common.telegramIdError": "❌ Не вдається визначити ваш Telegram ID. Будь ласка, спробуйте ще раз.", "common.welcome": "🛍️ Ласкаво просимо до PREM Bot!", "common.withdrawalErrorFallback": "Не вдалося вивести подарунок", "gifts.depositSuccess": "🎁 Подарунок успішно внесено! Тепер ви можете побачити свій подарунок в додатку PREM у вкладці 'Мої подарунки'.", "gifts.fetchingGifts": "🔄 Завантажуємо ваші подарунки...", "gifts.noGiftsAvailable": "📭 Подарунки недоступні. Відправте подарунки {relayerUsername} щоб почати!", "gifts.noLinkedOrder": "📦 Немає пов'язаного замовлення", "gifts.unknownGift": "Невідомий подарунок", "language.detectionPromptRussian": "🌍 Ми виявили, що ви використовуєте додаток російською мовою. Хочете переключитися на російську?", "language.detectionPromptUkrainian": "🌍 Ми виявили, що ви використовуєте додаток українською мовою. Хочете переключитися на українську?", "language.keepEnglish": "Ні, я хочу залишитися на англійській", "language.setToEnglish": "✅ Мову встановлено на англійську. Ласкаво просимо до PREM Bot!", "language.setToRussian": "✅ Язык изменен на русский. Добро пожаловать в PREM Bot!", "language.setToUkrainian": "✅ Мову змінено на українську. Ласкаво просимо до PREM Bot!", "language.switchToRussian": "Так, давайте переключимося на російську", "language.switchToUkrainian": "Так, давайте переключимося на українську", "orders.buyOrdersTitle": "🛒 Ваші замовлення на покупку (всього {count})", "orders.fetchingBuy": "🔄 Завантажуємо ваші замовлення на покупку...", "orders.fetchingSell": "🔄 Завантажуємо ваші замовлення на продаж...", "simulation.giftDepositError": "❌ Не вдалося внести тестовий подарунок: {errorMessage}\n\nБудь ласка, спробуйте ще раз або зверніться до підтримки, якщо проблема не зникне.", "simulation.giftDepositMode": "🎁 Внести подарунок (Режим симуляції)\n\n🔧 РЕЖИМ СИМУЛЯЦІЇ: Генеруємо та вносимо тестовий подарунок...", "simulation.giftDepositSuccess": "🎁 Деталі подарунка:\n• Назва: {giftName}\n• Модель: {modelName}\n• Символ: {symbolName}\n• Фон: {backdropName}\n\nВаш подарунок тепер доступний в додатку PREM у вкладці 'Мої подарунки'.", "simulation.giftWithdrawalError": "❌ Виведення подарунка не вдалося в режимі симуляції: {errorMessage}\n\nБудь ласка, спробуйте ще раз або зверніться до підтримки, якщо проблема не зникне.", "simulation.giftWithdrawalMode": "🎁 Виведення подарунка (Режим симуляції)\n\n🔧 РЕЖИМ СИМУЛЯЦІЇ: Обробляємо виведення подарунка...", "simulation.giftWithdrawalSuccess": "✅ Виведення подарунка успішно завершено в режимі симуляції!\n\n🔧 РЕЖИМ СИМУЛЯЦІЇ: Передача подарунка пропущена. В реальному режимі ваш подарунок був би переданий вам зараз.", "simulation.orderActivation": "🔧 РЕЖИМ РОЗРОБКИ: Це симуляція. В продакшені вам потрібно спочатку внести подарунок.", "simulation.orderView": "🔧 РЕЖИМ РОЗРОБКИ: Це замовлення в режимі симуляції.", "simulation.sellerGiftDeposit": "🔧 РЕЖИМ РОЗРОБКИ: Це симуляція. В продакшені ви б відправили подарунок @premrelayer.", "support.contactInfo": "📞 Для підтримки, будь ласка, зверніться до @prem_support_official"}