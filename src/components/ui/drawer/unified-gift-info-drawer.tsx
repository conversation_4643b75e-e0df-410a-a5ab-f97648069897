'use client';

import { ExternalLink, Gift } from 'lucide-react';
import { useIntl } from 'react-intl';

import { Button } from '@/components/ui/button';
import { TELEGRAM_BOT_URL } from '@/core.constants';

import { BaseDrawer } from './base-drawer';
import { DrawerHeader } from './drawer-header';
import { unifiedGiftInfoDrawerMessages } from './unified-gift-info-drawer/intl/unified-gift-info-drawer.messages';

export enum GiftDrawerMode {
  SEND_GIFT = 'send_gift',
  GET_GIFT = 'get_gift',
  ACTIVATE_ORDER = 'activate_order',
  GET_CANCELLED_GIFT = 'get_cancelled_gift',
}

interface UnifiedGiftInfoDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  mode: GiftDrawerMode;
}

interface DrawerConfig {
  title: string;
  subtitle: string;
  instructions: (React.ReactNode | string)[];
}

export function UnifiedGiftInfoDrawer({
  open,
  onOpenChange,
  mode,
}: UnifiedGiftInfoDrawerProps) {
  const { formatMessage: t } = useIntl();

  const getDrawerConfig = (mode: GiftDrawerMode): DrawerConfig => {
    const botLink = (
      <a
        href={TELEGRAM_BOT_URL}
        target="_blank"
        rel="noopener noreferrer"
        className="text-purple-400 hover:text-purple-300 underline"
      >
        @{process.env.NEXT_PUBLIC_TELEGRAM_BOT_NAME}
      </a>
    );

    const relayerLink = (
      <a
        href="https://t.me/premrelayer"
        target="_blank"
        rel="noopener noreferrer"
        className="text-purple-400 hover:text-purple-300 underline"
      >
        @premrelayer
      </a>
    );

    switch (mode) {
      case GiftDrawerMode.SEND_GIFT:
        return {
          title: t(unifiedGiftInfoDrawerMessages.sendGiftToRelayer),
          subtitle: t(unifiedGiftInfoDrawerMessages.sendGiftSteps),
          instructions: [
            t(unifiedGiftInfoDrawerMessages.goToBot, { botLink }),
            t(unifiedGiftInfoDrawerMessages.pressMySellOrdersPaid),
            t(unifiedGiftInfoDrawerMessages.selectOrderToSend),
            t(unifiedGiftInfoDrawerMessages.confirmAndSendToRelayer, {
              relayerLink,
            }),
          ],
        };

      case GiftDrawerMode.GET_GIFT:
        return {
          title: t(unifiedGiftInfoDrawerMessages.claimYourGift),
          subtitle: t(unifiedGiftInfoDrawerMessages.claimGiftSteps),
          instructions: [
            t(unifiedGiftInfoDrawerMessages.goToBot, { botLink }),
            t(unifiedGiftInfoDrawerMessages.pressMuyBuyOrders),
            t(unifiedGiftInfoDrawerMessages.selectOrderToGet),
            t(unifiedGiftInfoDrawerMessages.confirmAndGoToRelayer, {
              relayerLink,
            }),
          ],
        };

      case GiftDrawerMode.ACTIVATE_ORDER:
        return {
          title: t(unifiedGiftInfoDrawerMessages.activateYourOrder),
          subtitle: t(unifiedGiftInfoDrawerMessages.activateOrderSteps),
          instructions: [
            t(unifiedGiftInfoDrawerMessages.depositGiftToBot),
            t(unifiedGiftInfoDrawerMessages.attachGiftToOrder),
            t(unifiedGiftInfoDrawerMessages.goToBot, { botLink }),
            t(unifiedGiftInfoDrawerMessages.pressMySellOrdersWaitingActivation),
            t(unifiedGiftInfoDrawerMessages.selectOrderToActivate),
            t(unifiedGiftInfoDrawerMessages.sendGiftToRelayerToActivate, {
              relayerLink,
            }),
          ],
        };

      case GiftDrawerMode.GET_CANCELLED_GIFT:
        return {
          title: t(unifiedGiftInfoDrawerMessages.getCancelledGift),
          subtitle: t(unifiedGiftInfoDrawerMessages.getCancelledGiftSteps),
          instructions: [
            t(unifiedGiftInfoDrawerMessages.goToBot, { botLink }),
            t(unifiedGiftInfoDrawerMessages.pressMySellOrdersCancelled),
            t(unifiedGiftInfoDrawerMessages.selectOrderToActivate),
            t(unifiedGiftInfoDrawerMessages.goToRelayerToRetrieve, {
              relayerLink,
            }),
          ],
        };

      default:
        return {
          title: 'Gift Information',
          subtitle: 'Follow the instructions below',
          instructions: [],
        };
    }
  };

  const config = getDrawerConfig(mode);

  const handleOpenBot = () => {
    window.open(TELEGRAM_BOT_URL, '_blank');
  };

  return (
    <BaseDrawer open={open} onOpenChange={onOpenChange}>
      <DrawerHeader
        icon={Gift}
        iconClassName="w-6 h-6 text-purple-400"
        title={config.title}
        subtitle={config.subtitle}
      />

      <div className="space-y-4">
        <div className="bg-[#232e3c] border border-[#3a4a5c] rounded-lg p-4">
          <h4 className="text-sm font-medium text-[#f5f5f5] mb-3">
            Instructions:
          </h4>
          <ol className="space-y-2">
            {config.instructions.map((instruction, index) => (
              <li key={index} className="flex items-start gap-3">
                <span className="flex-shrink-0 w-6 h-6 bg-purple-500/20 text-purple-400 rounded-full flex items-center justify-center text-xs font-semibold">
                  {index + 1}
                </span>
                <span className="text-sm text-[#708499] leading-relaxed">
                  {instruction}
                </span>
              </li>
            ))}
          </ol>
        </div>

        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="flex-1 border-[#3a4a5c] text-[#708499] hover:bg-[#232e3c]"
          >
            Close
          </Button>
          <Button
            onClick={handleOpenBot}
            className="flex-1 bg-purple-500 hover:bg-purple-600 text-white"
          >
            <ExternalLink className="w-4 h-4 mr-2" />
            Open Bot
          </Button>
        </div>
      </div>
    </BaseDrawer>
  );
}
