import { defineMessages } from 'react-intl';

export const attachGiftToOrderDrawerMessages = defineMessages({
  title: {
    id: 'attachGiftToOrderDrawer.title',
    defaultMessage: 'Attach Gift to Order',
  },
  instructionsTitle: {
    id: 'attachGiftToOrderDrawer.instructionsTitle',
    defaultMessage: 'How to activate your order:',
  },
  instructionStep1: {
    id: 'attachGiftToOrderDrawer.instructionStep1',
    defaultMessage:
      'Send the gift directly to the relayer, then return to the app and attach the corresponding gift to your order',
  },

  selectGift: {
    id: 'attachGiftToOrderDrawer.selectGift',
    defaultMessage: 'Select a gift to attach',
  },
  noGiftsAvailable: {
    id: 'attachGiftToOrderDrawer.noGiftsAvailable',
    defaultMessage: 'No gifts available for this collection',
  },
  linkGiftToOrder: {
    id: 'attachGiftToOrderDrawer.linkGiftToOrder',
    defaultMessage: 'Link Gift to Order',
  },
  linking: {
    id: 'attachGiftToOrderDrawer.linking',
    defaultMessage: 'Linking...',
  },
  giftLinkedSuccessfully: {
    id: 'attachGiftToOrderDrawer.giftLinkedSuccessfully',
    defaultMessage: 'Gift linked to order successfully',
  },
  errorLoadingGifts: {
    id: 'attachGiftToOrderDrawer.errorLoadingGifts',
    defaultMessage: 'Error loading available gifts',
  },
  errorLinkingGift: {
    id: 'attachGiftToOrderDrawer.errorLinkingGift',
    defaultMessage: 'Error linking gift to order',
  },
});
