import { Telegraf } from "telegraf";
import {
  handleContactSupportButton,
  handleGetMyGiftsButton,
  handleDepositGiftButton,
} from "./handlers/buttons";
import {
  handleBackToMenuCallback,
  handleContactSupportCallback,
  handleOpenMarketplaceCallback,
  handleOrderHelpCallback,
} from "./handlers/callbacks/index";
import {
  handleHelpCommand,
  handleStartCommand,
  handleHealthCommand,
} from "./handlers/commands";
import { businessConnectionMiddleware } from "./middleware/business-connection";
import {
  languageDetectionMiddleware,
  handleLanguageSwitch,
} from "./middleware/language-detection";
import { BotLogger } from "./bot.logger";
import { loadEnvironment } from "./config/env-loader";
import { BotCommands, CallbackActions } from "./constants/bot-commands";
import { ButtonTextPatterns } from "./utils/button-text-matcher";
import { T } from "./i18n";
import { botMessages } from "./intl/messages";

loadEnvironment();

const BOT_TOKEN = process.env.BOT_TOKEN;

if (!BOT_TOKEN) {
  throw new Error("BOT_TOKEN is required in environment variables list.");
}

const bot = new Telegraf(BOT_TOKEN);

bot.use(businessConnectionMiddleware);
bot.use(languageDetectionMiddleware);

bot.start(handleStartCommand);
bot.help(handleHelpCommand);
bot.command(BotCommands.HEALTH, handleHealthCommand);

bot.hears(ButtonTextPatterns.MY_GIFTS, handleGetMyGiftsButton);
bot.hears(ButtonTextPatterns.DEPOSIT_GIFT, handleDepositGiftButton);
bot.hears(ButtonTextPatterns.CONTACT_SUPPORT, handleContactSupportButton);

bot.action(CallbackActions.ORDER_HELP, handleOrderHelpCallback);
bot.action(CallbackActions.CONTACT_SUPPORT, handleContactSupportCallback);
bot.action(CallbackActions.OPEN_MARKETPLACE, handleOpenMarketplaceCallback);

bot.action(CallbackActions.BACK_TO_MENU, handleBackToMenuCallback);

// Language switch callbacks
bot.action(/^lang_/, handleLanguageSwitch);

bot.catch((err, ctx) => {
  BotLogger.logBotError({
    error: err,
    chatId: ctx?.chat?.id as number,
    userId: ctx?.from?.id as number,
  });
  ctx?.reply?.(T(ctx, botMessages.botGenericError.id));
});

export default bot;
